import 'package:builder_konnect_mgt/core/core.dart';

const String resolveIssuesState = "resolveIssuesState";
const String attachImagesState = "attachImagesState";
const String updateTrackingState = "updateTrackingState";
const String assignDeliveryState = "assignDeliveryState";
const String getVehicleTypesState = "getVehicleTypesState";
const String getTrackingStatusesState = "getTrackingStatusesState";

class DeliveryVm extends BaseVm {
  List<OrderModel> _orderDeliveries = [];
  List<OrderModel> get orderDeliveries => _orderDeliveries;

  OrderDetailModel? _orderDetailModel;
  OrderDetailModel? get orderDetailModel => _orderDetailModel;

  Future<ApiResponse> fulfilmentDeliveries({
    String? dateFilter,
    String? status,
    String? query,
    int? sortBy,
  }) async {
    UriBuilder uriBuilder = UriBuilder("/api/v1/fulfilment-officers/deliveries")
      ..addQueryParameterIfNotEmpty("date_filter", dateFilter ?? '')
      ..addQueryParameterIfNotEmpty("status", status ?? '')
      ..addQueryParameterIfNotEmpty("q", query ?? '')
      ..addQueryParameterIfNotEmpty("sort_by", sortBy?.toString() ?? '');
    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      isFormData: true,
      onSuccess: (data) {
        _orderDeliveries =
            orderModelFromJson(json.encode(data["data"]["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse<OrderDetailModel>> viewDelivery(String id) async {
    return await performApiCall<OrderDetailModel>(
      url: "/api/v1/fulfilment-officers/deliveries/$id",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _orderDetailModel = null;
        _orderDetailModel = orderDetailModelFromJson(json.encode(data["data"]));
        return ApiResponse(
          success: true,
          data: _orderDetailModel,
        );
      },
    );
  }

  Future<ApiResponse<List<String>>> getVehicleTypes() async {
    return await performApiCall<List<String>>(
      url: "/api/v1/fulfilment-officers/deliveries/all/vehicle-types",
      method: apiService.getWithAuth,
      busyObjectName: getVehicleTypesState,
      onSuccess: (data) {
        if (data is Map && data.containsKey("data") && data["data"] is List) {
          return ApiResponse(
            success: true,
            data: (data["data"] as List).map((e) => e.toString()).toList(),
          );
        }
        return ApiResponse(
          success: false,
          message: "Invalid data format",
          data: [],
        );
      },
    );
  }

  Future<ApiResponse<List<String>>> getTrackingStatuses() async {
    return await performApiCall<List<String>>(
      url: "/api/v1/fulfilment-officers/deliveries/all/statuses",
      method: apiService.getWithAuth,
      busyObjectName: getTrackingStatusesState,
      onSuccess: (data) {
        if (data is Map && data.containsKey("data") && data["data"] is List) {
          return ApiResponse(
            success: true,
            data: (data["data"] as List).map((e) => e.toString()).toList(),
          );
        }
        return ApiResponse(
          success: false,
          message: "Invalid data format",
          data: [],
        );
      },
    );
  }

  Future<ApiResponse> attachImages({
    required String id,
    required List<String> documentUrls,
  }) async {
    return await performApiCall(
      url: "/api/v1/fulfilment-officers/deliveries/$id/attach-media",
      method: apiService.putWithAuth,
      busyObjectName: attachImagesState,
      body: {
        "media": documentUrls,
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> assignDelivery({
    required String id,
    required String name,
    required String phone,
    required String email,
    required String vehicleType,
  }) async {
    return await performApiCall(
      url:
          "/api/v1/fulfilment-officers/deliveries/$id/assign-to-delivery-officer",
      method: apiService.putWithAuth,
      busyObjectName: assignDeliveryState,
      body: {
        "name": name,
        "phone": phone,
        "email": email,
        "vehicle_type": vehicleType,
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> resolveIssues({
    required String id,
    required String comment,
  }) async {
    return await performApiCall(
      url: "/api/v1/fulfilment-officers/deliveries/$id/add-comment",
      method: apiService.putWithAuth,
      busyObjectName: resolveIssuesState,
      body: {"comment": comment},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> updateTracking({
    required String id,
    required String status,
  }) async {
    return await performApiCall(
      url: "/api/v1/fulfilment-officers/deliveries/$id/status",
      method: apiService.putWithAuth,
      busyObjectName: updateTrackingState,
      body: {"status": status},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }
}

final deliveryVmodel = ChangeNotifierProvider((ref) {
  return DeliveryVm();
});
