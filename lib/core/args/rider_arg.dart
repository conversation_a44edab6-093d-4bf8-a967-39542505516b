class RiderArg {
  final String name;
  final String phone;
  final String email;
  final String? bankName;
  final String? accountNumber;
  final String? accountName;

  RiderArg({
    required this.name,
    required this.phone,
    required this.email,
    this.bankName,
    this.accountNumber,
    this.accountName,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'phone': phone,
      'email': email,
      'bank_name': bankName,
      'account_number': accountNumber,
      'account_name': accountName,
    };
  }
}
