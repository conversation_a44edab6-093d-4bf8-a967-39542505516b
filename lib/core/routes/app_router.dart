import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/screens/screens.dart';
import 'package:flutter/cupertino.dart';

class AppRouter {
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    final args = settings.arguments;

    CupertinoPageRoute buildRoute(Widget widget) =>
        CupertinoPageRoute(builder: (_) => widget);

    switch (settings.name) {
      case RoutePath.splashScreen:
        return buildRoute(const SplashScreen());
      case RoutePath.bottomNavScreen:
        return buildRoute(const BottomNavScreen());

      //  Auth
      case RoutePath.loginScreen:
        return buildRoute(const LoginScreen());
      case RoutePath.forgotPasswordScreen:
        return buildRoute(const ForgotPasswordScreen());
      case RoutePath.forgotPasswordOtpScreen:
        if (args is ForgotArg) {
          return buildRoute(ForgotPasswordOtpScreen(arg: args));
        }
        return errorScreen('No route defined for ${settings.name}');
      case RoutePath.newPasswordScreen:
        if (args is ForgotArg) {
          return buildRoute(NewPasswordScreen(arg: args));
        }
        return errorScreen('No route defined for ${settings.name}');

      // Order
      case RoutePath.orderDetailsScreen:
        if (args is String) {
          return buildRoute(OrderDetailsScreen(id: args));
        }
        return errorScreen('No route defined for ${settings.name}');

      case RoutePath.notificationScreen:
        return buildRoute(const NotificationScreen());

      // Wallet
      case RoutePath.sendMoneyScreen:
        return buildRoute(const SendMoneyScreen());
      case RoutePath.sendMoneyAccountScreen:
        return buildRoute(const SendMoneyAccountScreen());
      case RoutePath.sendMoneySuccesScreen:
        return buildRoute(const SendMoneySuccesScreen());

      default:
        return errorScreen('No route defined for ${settings.name}');
    }
  }

  static CupertinoPageRoute errorScreen(String msg) {
    return CupertinoPageRoute(
      builder: (_) => Scaffold(
        body: Center(
          child: Text(msg),
        ),
      ),
    );
  }
}
