import 'dart:convert';

List<OrderModel> orderModelFromJson(String str) =>
    List<OrderModel>.from(json.decode(str).map((x) => OrderModel.fromJson(x)));

String orderModelToJson(List<OrderModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class OrderModel {
  final String? id;
  final String? orderNumber;
  final String? receiptNo;
  final String? status;
  final DateTime? createdAt;

  OrderModel({
    this.id,
    this.orderNumber,
    this.receiptNo,
    this.status,
    this.createdAt,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) => OrderModel(
        id: json["id"],
        orderNumber: json["order_number"],
        receiptNo: json["receipt_no"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "order_number": orderNumber,
        "receipt_no": receiptNo,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
      };
}
