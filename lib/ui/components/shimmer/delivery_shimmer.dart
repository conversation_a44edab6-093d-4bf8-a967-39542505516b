import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class DeliveryShimmer extends StatelessWidget {
  const DeliveryShimmer({
    super.key,
    this.count,
  });

  final int? count;

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: ListView.separated(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        padding: EdgeInsets.only(
          top: Sizer.height(24),
          bottom: Sizer.height(50),
        ),
        itemBuilder: (_, i) {
          return OrderListTile(
            orderModel: OrderModel(
              id: "1",
              orderNumber: "1234567890",
              receiptNo: "122",
              status: "Processing",
              createdAt: DateTime.now(),
            ),
          );
        },
        separatorBuilder: (_, __) => YBox(24),
        itemCount: count ?? 4,
      ),
    );
  }
}
