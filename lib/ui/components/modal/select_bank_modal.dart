import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class SelectBankModal extends ConsumerStatefulWidget {
  const SelectBankModal({
    super.key,
  });

  @override
  ConsumerState<SelectBankModal> createState() => _SelectBankModalState();
}

class _SelectBankModalState extends ConsumerState<SelectBankModal> {
  @override
  Widget build(BuildContext context) {
    // final deliveryVm = ref.watch(deliveryVmodel);
    return Container(
      height: Sizer.screenHeight * 0.8,
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          YBox(20),
          Row(
            children: [
              Text("Select Bank", style: AppTypography.text16.medium),
              Spacer(),
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Icon(
                  Icons.close,
                  color: AppColors.black,
                  size: Sizer.radius(24),
                ),
              )
            ],
          ),
          YBox(16),
          Divider(color: AppColors.neutral4, height: 1),
          YBox(24),
          CustomTextField(
            hintText: "Search Bank Name",
            borderRadius: 8,
            prefixIcon: Icon(
              Iconsax.search_normal_1,
              size: Sizer.radius(16),
              color: AppColors.black.withValues(alpha: 0.85),
            ),
          ),
          YBox(7),
          Expanded(
            child: ListView.separated(
              padding: EdgeInsets.only(
                top: Sizer.height(10),
                bottom: Sizer.height(80),
              ),
              shrinkWrap: true,
              itemCount: 20,
              separatorBuilder: (_, __) => YBox(12),
              itemBuilder: (_, i) => Row(
                children: [
                  SvgPicture.asset(AppSvgs.logomark),
                  XBox(10),
                  Text(
                    "AB Microfinance",
                    style: AppTypography.text14.withCustomColor(
                      AppColors.black.withValues(alpha: 0.85),
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
