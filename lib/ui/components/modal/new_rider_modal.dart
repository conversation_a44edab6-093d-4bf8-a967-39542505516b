import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class NewRiderModal extends ConsumerStatefulWidget {
  const NewRiderModal({super.key});

  @override
  ConsumerState<NewRiderModal> createState() => _NewRiderModalState();
}

class _NewRiderModalState extends ConsumerState<NewRiderModal> {
  final nameC = TextEditingController();
  final phoneC = TextEditingController();
  final emailC = TextEditingController();
  final bankNameC = TextEditingController();
  final accountNumberC = TextEditingController();
  final accountNameC = TextEditingController();

  final nameF = FocusNode();
  final phoneF = FocusNode();
  final emailF = FocusNode();
  final bankNameF = FocusNode();
  final accountNumberF = FocusNode();
  final accountNameF = FocusNode();

  @override
  void dispose() {
    nameC.dispose();
    phoneC.dispose();
    emailC.dispose();
    bankNameC.dispose();
    accountNumberC.dispose();
    accountNameC.dispose();

    phoneF.dispose();
    nameF.dispose();
    emailF.dispose();
    bankNameF.dispose();
    accountNumberF.dispose();
    accountNameF.dispose();

    super.dispose();
  }

  bool get isEmailValid =>
      emailC.text.trim().contains("@") && emailC.text.trim().contains(".");
  bool get isFormValid {
    return nameC.text.isNotEmpty && phoneC.text.isNotEmpty && isEmailValid;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      // height: Sizer.screenHeight * 0.8,
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          YBox(20),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Text("Add New Rider", style: AppTypography.text16.medium),
          ),
          ListView(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
              bottom: Sizer.height(30),
            ),
            children: [
              YBox(24),
              CustomTextField(
                controller: nameC,
                focusNode: nameF,
                showLabelHeader: true,
                labelText: "Rider’s Name",
                hintText: "Enter Name",
                onChanged: (p0) => setState(() {}),
                onSubmitted: (value) {
                  phoneF.requestFocus();
                },
              ),
              YBox(12),
              CustomTextField(
                controller: phoneC,
                focusNode: phoneF,
                showLabelHeader: true,
                labelText: "Phone Number",
                hintText: "Enter Number",
                keyboardType: KeyboardType.phone,
                onChanged: (p0) => setState(() {}),
                onSubmitted: (value) {
                  emailF.requestFocus();
                },
              ),
              YBox(12),
              CustomTextField(
                controller: emailC,
                focusNode: emailF,
                keyboardType: KeyboardType.email,
                showLabelHeader: true,
                labelText: "Email Address",
                hintText: "Enter Address",
                errorText: emailC.text.isNotEmpty && !isEmailValid
                    ? "Invalid email"
                    : null,
                onChanged: (p0) => setState(() {}),
                onSubmitted: (value) {
                  FocusScope.of(context).unfocus();
                },
              ),
              // YBox(30),
              // Container(
              //   padding: EdgeInsets.symmetric(
              //     horizontal: Sizer.width(16),
              //     vertical: Sizer.height(12),
              //   ),
              //   decoration: BoxDecoration(
              //     color: AppColors.neutral2,
              //     borderRadius: BorderRadius.circular(Sizer.radius(8)),
              //     border: Border.all(color: AppColors.neutral5, width: 1),
              //   ),
              //   child: Column(
              //     mainAxisSize: MainAxisSize.min,
              //     crossAxisAlignment: CrossAxisAlignment.start,
              //     children: [
              //       Text(
              //         "Add rider’s payment details",
              //         style: AppTypography.text12.withCustomColor(
              //           AppColors.black.withValues(alpha: 0.85),
              //         ),
              //       ),
              //       YBox(12),
              //       CustomTextField(
              //         controller: bankNameC,
              //         focusNode: bankNameF,
              //         showLabelHeader: true,
              //         labelText: "Bank Name",
              //         hintText: "Enter Name",
              //         onChanged: (p0) => setState(() {}),
              //         onSubmitted: (value) {
              //           accountNumberF.requestFocus();
              //         },
              //       ),
              //       YBox(12),
              //       CustomTextField(
              //         controller: accountNumberC,
              //         focusNode: accountNumberF,
              //         showLabelHeader: true,
              //         labelText: "Account Number",
              //         hintText: "Enter Number",
              //         keyboardType: KeyboardType.phone,
              //         onChanged: (p0) => setState(() {}),
              //         onSubmitted: (value) {
              //           accountNameF.requestFocus();
              //         },
              //       ),
              //       YBox(12),
              //       CustomTextField(
              //         controller: accountNameC,
              //         focusNode: accountNameF,
              //         keyboardType: KeyboardType.email,
              //         showLabelHeader: true,
              //         labelText: "Account Name ",
              //         hintText: "Enter Name",
              //         onChanged: (p0) => setState(() {}),
              //         onSubmitted: (value) {
              //           FocusScope.of(context).unfocus();
              //         },
              //       ),
              //     ],
              //   ),
              // ),
              YBox(24),
              ref.watch(deliveryVmodel).busy("kk")
                  ? BtnLoadState()
                  : Row(
                      children: [
                        Expanded(
                          child: CustomBtn.solid(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            online: true,
                            isOutline: true,
                            textColor: AppColors.black.withValues(alpha: 0.45),
                            outlineColor:
                                AppColors.black.withValues(alpha: 0.45),
                            text: "Cancel",
                          ),
                        ),
                        XBox(12),
                        Expanded(
                          child: CustomBtn.solid(
                            onTap: () {
                              Navigator.pop(
                                context,
                                RiderArg(
                                  name: nameC.text.trim(),
                                  phone: phoneC.text.trim(),
                                  email: emailC.text.trim(),
                                ),
                              );
                            },
                            online: isFormValid,
                            text: "Save",
                          ),
                        ),
                      ],
                    ),
            ],
          ),
        ],
      ),
    );
  }
}
