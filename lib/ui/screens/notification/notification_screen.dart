import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class NotificationScreen extends ConsumerStatefulWidget {
  const NotificationScreen({super.key});

  @override
  ConsumerState<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends ConsumerState<NotificationScreen> {
  @override
  initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() async {
    await ref.read(notificationVmodel).getNotifications();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: "Notifications",
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        children: [
          const YBox(40),
          Row(
            children: [
              SvgPicture.asset(AppSvgs.calendar),
              XBox(8),
              Text(
                "TODAY",
                style: AppTypography.text14.medium,
              ),
              XBox(8),
              Expanded(child: HLine())
            ],
          ),
          YBox(16),
          ListView.separated(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: 5, // Replace with actual number of notifications
            separatorBuilder: (_, __) => YBox(16),
            itemBuilder: (_, i) => NotificationCard(),
          ),
        ],
      ),
    );
  }
}

class NotificationCard extends StatelessWidget {
  const NotificationCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: Sizer.height(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "New Delivery Request",
                  style: AppTypography.text14.medium
                      .withCustomColor(AppColors.blue4F),
                ),
                YBox(8),
                Text(
                  "You have a new order",
                  style: AppTypography.text12.withCustomColor(
                    AppColors.blue4F,
                  ),
                ),
              ],
            ),
          ),
          Text(
            "2 mins ago",
            style: AppTypography.text12.withCustomColor(
              AppColors.gray500,
            ),
          ),
        ],
      ),
    );
  }
}
