import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/ui.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  FilterArg? _filterArg;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(dashboardVmodel).getDashboardStats();
      ref.read(deliveryVmodel).fulfilmentDeliveries();
    });
  }

  @override
  Widget build(BuildContext context) {
    final dashboardVm = ref.watch(dashboardVmodel);
    final deliveryVm = ref.watch(deliveryVmodel);
    return Scaffold(
      appBar: CustomAppbar(
        title: "Dashboard",
        trailingWidget: InkWell(
          onTap: () {
            Navigator.pushNamed(context, RoutePath.notificationScreen);
          },
          child: SvgPicture.asset(AppSvgs.notification),
        ),
        leadingWidget: Container(
          height: Sizer.height(40),
          width: Sizer.width(40),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Sizer.radius(40)),
              border: Border.all(
                color: AppColors.primaryBlue,
                width: 2,
              )),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(Sizer.radius(40)),
            child: ref.watch(authVmodel).user?.avatar != null
                ? MyCachedNetworkImage(
                    imageUrl: ref.watch(authVmodel).user!.avatar,
                    fit: BoxFit.cover,
                  )
                : Icon(
                    Iconsax.user,
                    size: Sizer.width(20),
                  ),
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            YBox(20),
            Skeletonizer(
              enabled: dashboardVm.isBusy,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text("Overview", style: AppTypography.text20.medium),
                      Spacer(),
                      InkWell(
                        onTap: () async {
                          final result = await ModalWrapper.bottomSheet(
                            context: context,
                            widget: FilterDataModal(
                              filterArg: _filterArg,
                            ),
                          );

                          if (result is FilterArg) {
                            _filterArg = result;
                            ref
                                .read(dashboardVmodel)
                                .getDashboardStats(dateFilter: result.filter);
                          }
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(16),
                            vertical: Sizer.height(8),
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: AppColors.neutral5,
                            ),
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(4)),
                          ),
                          child: Row(
                            children: [
                              Text(
                                _filterArg?.isCustom == true
                                    ? "Custom Date"
                                    : _filterArg?.filter ?? "This month",
                                style: AppTypography.text14,
                              ),
                              const XBox(4),
                              Icon(
                                Icons.keyboard_arrow_down_rounded,
                                size: 20,
                                color: AppColors.neutral9,
                              )
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                  Text(
                    "Fulfilment order summary",
                    style: AppTypography.text14.copyWith(
                        color: AppColors.black.withValues(alpha: 0.45)),
                  ),
                ],
              ),
            ),
            YBox(16),
            LoadableContentBuilder(
                isBusy: dashboardVm.isBusy,
                loadingBuilder: (context) {
                  return StatShimmer();
                },
                contentBuilder: (context) {
                  return Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: StatCard(
                              title: "Total Orders",
                              amount: "₦${AppUtils.formatNumber(
                                number: dashboardVm.statModel?.totalOrders ?? 0,
                                decimalPlaces: 2,
                              )}",
                              bgColor: AppColors.blueFF,
                              borderColor: AppColors.blue5,
                              amountColor: AppColors.primaryBlue,
                              onTap: () {},
                            ),
                          ),
                          XBox(16),
                          Expanded(
                            child: StatCard(
                              title: "Pending",
                              amount: AppUtils.formatNumber(
                                number: dashboardVm.statModel?.pending ?? 0,
                              ),
                              bgColor: AppColors.magentaF8,
                              borderColor: AppColors.magenta4,
                              borderButtomColor: AppColors.magenta2,
                              amountColor: AppColors.magenta6,
                              onTap: () {},
                            ),
                          ),
                        ],
                      ),
                      YBox(16),
                      Row(
                        children: [
                          Expanded(
                            child: StatCard(
                              title: "In Progress",
                              amount:
                                  "${AppUtils.formatNumber(number: dashboardVm.statModel?.inprogress ?? 0)}%",
                              borderButtomColor: AppColors.purple2,
                              bgColor: AppColors.yellowE6,
                              borderColor: AppColors.yellow4,
                              amountColor: AppColors.yellow6,
                              onTap: () {},
                            ),
                          ),
                          XBox(16),
                          Expanded(
                            child: StatCard(
                              title: "Completed",
                              amount: "${AppUtils.formatNumber(
                                number: dashboardVm.statModel?.completed ?? 0,
                              )}%",
                              bgColor: AppColors.greenED,
                              borderColor: AppColors.green4,
                              borderButtomColor: AppColors.purple2,
                              amountColor: AppColors.green7,
                              iconPath: AppSvgs.chart,
                              onTap: () {},
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                }),
            YBox(24),
            Skeletonizer(
              enabled: dashboardVm.isBusy,
              child:
                  Text("Recent Deliveries", style: AppTypography.text20.medium),
            ),
            Skeletonizer(
              enabled: dashboardVm.isBusy,
              child: Text(
                "View and manage all recent deliveries",
                style: AppTypography.text14.withCustomColor(
                  AppColors.black.withValues(alpha: 0.45),
                ),
              ),
            ),
            Expanded(
              child: LoadableContentBuilder(
                isBusy: deliveryVm.isBusy,
                items: deliveryVm.orderDeliveries,
                loadingBuilder: (context) {
                  return DeliveryShimmer();
                },
                emptyBuilder: (context) {
                  return Center(
                    child: Text(
                      "No recent deliveries",
                      style: AppTypography.text14.copyWith(
                        fontWeight: FontWeight.w500,
                        color: AppColors.gray500,
                      ),
                    ),
                  );
                },
                contentBuilder: (context) {
                  final recentDeliveries =
                      deliveryVm.orderDeliveries.take(4).toList();
                  return ListView.separated(
                    padding: EdgeInsets.only(
                        top: Sizer.height(24), bottom: Sizer.height(50)),
                    separatorBuilder: (_, __) => YBox(24),
                    itemCount: recentDeliveries.length,
                    itemBuilder: (_, i) {
                      final o = recentDeliveries[i];
                      return OrderListTile(orderModel: o);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
