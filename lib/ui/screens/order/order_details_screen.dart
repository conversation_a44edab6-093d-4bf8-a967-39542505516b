import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class OrderDetailsScreen extends ConsumerStatefulWidget {
  const OrderDetailsScreen({
    super.key,
    required this.id,
  });

  final String id;

  @override
  ConsumerState<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends ConsumerState<OrderDetailsScreen> {
  bool _expandedTrackingHistory = false;
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _trackingHistorySectionKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _init();
    });
  }

  _init() async {
    await ref.read(deliveryVmodel).viewDelivery(widget.id);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToTrackingHistory() {
    if (_expandedTrackingHistory) {
      // Add a small delay to allow the expanded content to render first
      Future.delayed(const Duration(milliseconds: 200), () {
        if (_trackingHistorySectionKey.currentContext != null) {
          Scrollable.ensureVisible(
            _trackingHistorySectionKey.currentContext!,
            duration: const Duration(milliseconds: 400),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final orderDetailVm = ref.watch(deliveryVmodel);
    return Scaffold(
      appBar: CustomAppbar(
        title: "Order Detail",
        trailingWidget: InkWell(
          child: SvgPicture.asset(AppSvgs.more),
          onTap: () {
            showMenu(
              context: context,
              position: RelativeRect.fromLTRB(100, 100, 0, 0),
              items: [
                PopupMenuItem(
                  value: 'assign',
                  child: Text('Assign Delivery'),
                ),
                PopupMenuItem(
                  value: 'attach',
                  child: Text('Attach Images'),
                ),
                PopupMenuItem(
                  value: 'resolve',
                  child: Text('Resolve Issues'),
                ),
              ],
            ).then((value) {
              // Handle the selected option
              if (value != null) {
                // Implement the action for the selected option
                printty('Selected: $value');
                ModalWrapper.bottomSheet(
                  context: context.mounted
                      ? context
                      : NavKey.appNavKey.currentContext!,
                  widget: switch (value) {
                    'assign' => AssignDeliveryModal(deliveryId: widget.id),
                    'attach' => AttachImagesModal(deliveryId: widget.id),
                    'resolve' => ResolveIssuesModal(deliveryId: widget.id),
                    _ => const SizedBox(),
                  },
                );
              }
            });
          },
        ),
      ),
      body: LoadableContentBuilder(
        isBusy: ref.watch(deliveryVmodel).isBusy,
        isError: ref.watch(deliveryVmodel).hasError,
        loadingBuilder: (context) {
          return SizerLoader(height: 600);
        },
        errorBuilder: (p0) {
          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text("Error loading order details"),
                YBox(20),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
                  child: CustomBtn.solid(
                    onTap: () {
                      _init();
                    },
                    online: true,
                    text: "Retry",
                  ),
                ),
              ],
            ),
          );
        },
        contentBuilder: (context) {
          return RefreshIndicator(
            onRefresh: () async {
              await _init();
            },
            child: ListView(
              controller: _scrollController,
              padding: EdgeInsets.only(
                left: Sizer.width(16),
                right: Sizer.width(16),
                bottom: Sizer.height(100),
              ),
              children: [
                const YBox(16),
                Row(
                  children: [
                    Expanded(
                      child: ColumnText(
                        title: "Order ID",
                        subtitle:
                            "#${orderDetailVm.orderDetailModel?.orderNumber ?? ""}",
                      ),
                    ),
                    OrderStatus(
                        status: orderDetailVm.orderDetailModel?.status ??
                            "Pending"),
                  ],
                ),
                const YBox(16),
                Text(
                  "Vendor Details",
                  style: AppTypography.text14.medium.withCustomColor(
                    AppColors.black.withValues(alpha: 0.45),
                  ),
                ),
                YBox(10),
                Row(
                  children: [
                    ColumnText(
                      title: "Vendor",
                      subtitle:
                          orderDetailVm.orderDetailModel?.vendor?.name ?? "",
                    ),
                    Spacer(),
                    ColumnText(
                      title: "Contact",
                      subtitle:
                          orderDetailVm.orderDetailModel?.vendor?.contact ?? "",
                      crossAxisAlignment: CrossAxisAlignment.end,
                    ),
                  ],
                ),
                YBox(10),
                ColumnText(
                  title: "Vendor's Address",
                  subtitle:
                      orderDetailVm.orderDetailModel?.vendor?.address ?? "",
                ),
                YBox(10),
                HLine(),
                YBox(13),
                Text(
                  "Customer Details",
                  style: AppTypography.text14.medium.withCustomColor(
                    AppColors.black.withValues(alpha: 0.45),
                  ),
                ),
                YBox(10),
                Row(
                  children: [
                    ColumnText(
                      title: "Customer Name",
                      subtitle:
                          orderDetailVm.orderDetailModel?.customer?.name ?? "",
                    ),
                    Spacer(),
                    ColumnText(
                      title: "Contact",
                      subtitle:
                          orderDetailVm.orderDetailModel?.customer?.contact ??
                              "N/A",
                      crossAxisAlignment: CrossAxisAlignment.end,
                    ),
                  ],
                ),
                YBox(10),
                ColumnText(
                  title: "Shipping Address",
                  subtitle: orderDetailVm.orderDetailModel?.customer?.address ??
                      "N/A",
                ),
                YBox(10),
                HLine(),
                YBox(13),
                Text(
                  "Order Items",
                  style: AppTypography.text14.medium.withCustomColor(
                    AppColors.black.withValues(alpha: 0.45),
                  ),
                ),
                YBox(10),
                HLine(),
                YBox(13),
                Container(
                  padding: EdgeInsets.all(
                    Sizer.radius(10),
                  ),
                  decoration: BoxDecoration(
                      color: AppColors.neutral2,
                      borderRadius: BorderRadius.circular(Sizer.radius(8)),
                      border: Border.all(color: AppColors.neutral4, width: 1)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Item Breakdown",
                        style: AppTypography.text14.medium.withCustomColor(
                          AppColors.black.withValues(alpha: 0.45),
                        ),
                      ),
                      YBox(13),
                      ListView.separated(
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        physics: NeverScrollableScrollPhysics(),
                        itemBuilder: (_, i) {
                          final item =
                              orderDetailVm.orderDetailModel?.lineItems?[i];
                          return Row(
                            children: [
                              SizedBox(
                                height: Sizer.height(40),
                                width: Sizer.width(40),
                                child: MyCachedNetworkImage(
                                  imageUrl: item?.mediaUrl ?? "",
                                ),
                              ),
                              XBox(8),
                              Expanded(
                                child: Text(
                                  item?.product ?? "",
                                  style: AppTypography.text14.withCustomColor(
                                    AppColors.black.withValues(alpha: 0.85),
                                  ),
                                ),
                              ),
                              Text(
                                "${item?.quantity ?? 0}X",
                                style: AppTypography.text14.withCustomColor(
                                  AppColors.black.withValues(alpha: 0.45),
                                ),
                              ),
                              XBox(30),
                              Text(
                                "${item?.totalCost ?? 0}",
                                style: AppTypography.text14.withCustomColor(
                                  AppColors.black.withValues(alpha: 0.45),
                                ),
                              ),
                            ],
                          );
                        },
                        separatorBuilder: (_, __) => YBox(10),
                        itemCount:
                            orderDetailVm.orderDetailModel?.lineItems?.length ??
                                0,
                      ),
                      YBox(13),
                      CostRowText(
                        leftText: "Shipping",
                        rightText:
                            "${orderDetailVm.orderDetailModel?.fees?.serviceFee ?? 0}",
                      ),
                      YBox(13),
                      CostRowText(
                        leftText: "Sub Total",
                        rightText:
                            "${orderDetailVm.orderDetailModel?.subtotal ?? 0}",
                      ),
                      YBox(13),
                      CostRowText(
                        leftText: "VAT",
                        rightText:
                            "${orderDetailVm.orderDetailModel?.fees?.tax ?? 0}",
                      ),
                      YBox(13),
                      CostRowText(
                        leftText: "Total",
                        rightText:
                            "${orderDetailVm.orderDetailModel?.amount ?? 0}",
                      ),
                    ],
                  ),
                ),
                if (orderDetailVm.orderDetailModel?.deliveryAttachments !=
                        null &&
                    orderDetailVm.orderDetailModel?.deliveryAttachments!
                            .isNotEmpty ==
                        true)
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      YBox(13),
                      Text(
                        "Attachments",
                        style: AppTypography.text14.medium.withCustomColor(
                          AppColors.black.withValues(alpha: 0.45),
                        ),
                      ),
                      YBox(12),
                      Row(
                        children:
                            orderDetailVm.orderDetailModel?.deliveryAttachments
                                    ?.map((e) => Padding(
                                          padding: EdgeInsets.only(
                                              right: Sizer.width(10)),
                                          child: SizedBox(
                                            height: Sizer.height(100),
                                            width: Sizer.width(100),
                                            child: MyCachedNetworkImage(
                                              imageUrl: e,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ))
                                    .toList() ??
                                [],
                      ),
                    ],
                  ),
                YBox(13),
                if (orderDetailVm.orderDetailModel?.riderDetails != null)
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Driver",
                        style: AppTypography.text14.medium.withCustomColor(
                          AppColors.black.withValues(alpha: 0.45),
                        ),
                      ),
                      YBox(12),
                      Container(
                        padding: EdgeInsets.all(Sizer.radius(16)),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(Sizer.radius(8)),
                          border:
                              Border.all(color: AppColors.neutral4, width: 1),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              orderDetailVm
                                      .orderDetailModel?.riderDetails?.name ??
                                  "",
                              style: AppTypography.text14.medium,
                            ),
                            YBox(4),
                            Row(
                              children: [
                                Text(
                                  orderDetailVm.orderDetailModel?.riderDetails
                                          ?.phone ??
                                      "",
                                  style: AppTypography.text12.withCustomColor(
                                    AppColors.black.withValues(alpha: 0.45),
                                  ),
                                ),
                                XBox(8),
                                Container(
                                  height: Sizer.height(8),
                                  width: Sizer.width(8),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: AppColors.neutral5,
                                  ),
                                ),
                                XBox(8),
                                Text(
                                  orderDetailVm.orderDetailModel?.riderDetails
                                          ?.email ??
                                      "",
                                  style: AppTypography.text12.withCustomColor(
                                    AppColors.black.withValues(alpha: 0.45),
                                  ),
                                ),
                              ],
                            ),
                            YBox(12),
                            Row(
                              children: [
                                SvgPicture.asset(AppSvgs.package),
                                XBox(10),
                                Text(
                                  orderDetailVm.orderDetailModel?.riderDetails
                                          ?.vehicleType ??
                                      "",
                                  style: AppTypography.text12.withCustomColor(
                                    AppColors.black.withValues(alpha: 0.45),
                                  ),
                                ),
                              ],
                            ),
                            YBox(12),
                            Row(
                              children: [
                                Expanded(
                                  child: CustomBtn.solid(
                                    onTap: () {
                                      //  Navigator.pushNamed(context, RoutePath.bottomNavScreen);
                                    },
                                    online: true,
                                    isOutline: true,
                                    textColor:
                                        AppColors.black.withValues(alpha: 0.45),
                                    outlineColor:
                                        AppColors.black.withValues(alpha: 0.45),
                                    text: "Edit",
                                  ),
                                ),
                                XBox(12),
                                Expanded(
                                  child: CustomBtn.solid(
                                    onTap: () {
                                      ModalWrapper.bottomSheet(
                                          context: context,
                                          widget: UpdateTrackingModal(
                                              deliveryId: widget.id));
                                    },
                                    online: true,
                                    text: "Update Tracking",
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                YBox(13),
                if (orderDetailVm.orderDetailModel?.timeline != null &&
                    orderDetailVm.orderDetailModel?.timeline?.isNotEmpty ==
                        true)
                  Container(
                    key: _trackingHistorySectionKey,
                    padding: EdgeInsets.all(
                      Sizer.radius(16),
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.neutral6,
                      ),
                      borderRadius: BorderRadius.circular(Sizer.radius(4)),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              _expandedTrackingHistory =
                                  !_expandedTrackingHistory;
                              _scrollToTrackingHistory();
                            });
                          },
                          child: Row(
                            children: [
                              Text("Tracking History",
                                  style: AppTypography.text14.medium),
                              Spacer(),
                              AnimatedRotation(
                                turns: _expandedTrackingHistory ? 0.5 : 0.0,
                                duration: const Duration(milliseconds: 300),
                                child: Icon(
                                  Icons.keyboard_arrow_down_rounded,
                                  size: Sizer.radius(20),
                                  color: AppColors.neutral9,
                                ),
                              )
                            ],
                          ),
                        ),
                        AnimatedSize(
                          duration: const Duration(milliseconds: 400),
                          curve: Curves.easeInOut,
                          alignment: Alignment.topCenter,
                          child: !_expandedTrackingHistory
                              ? SizedBox.shrink()
                              : Padding(
                                  padding: EdgeInsets.only(
                                    top: Sizer.height(28),
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Delivery Time",
                                        style: AppTypography.text14
                                            .withCustomColor(
                                          AppColors.black
                                              .withValues(alpha: 0.45),
                                        ),
                                      ),
                                      if (orderDetailVm
                                              .orderDetailModel?.deliveryDate !=
                                          null)
                                        Padding(
                                          padding: EdgeInsets.only(
                                            top: Sizer.height(4),
                                          ),
                                          child: Text(
                                            orderDetailVm.orderDetailModel
                                                    ?.deliveryDate ??
                                                "",
                                            style: AppTypography.text20.medium
                                                .withCustomColor(
                                              AppColors.black
                                                  .withValues(alpha: 0.85),
                                            ),
                                          ),
                                        ),
                                      YBox(10),
                                      ListView.separated(
                                        shrinkWrap: true,
                                        physics: NeverScrollableScrollPhysics(),
                                        padding: EdgeInsets.only(
                                          top: Sizer.height(10),
                                        ),
                                        itemCount: 5,
                                        separatorBuilder: (_, __) =>
                                            SizedBox.shrink(),
                                        itemBuilder: (_, i) {
                                          final timeline = orderDetailVm
                                              .orderDetailModel?.timeline?[i];
                                          return TimelineWidget(
                                            shortDesc:
                                                timeline?.shortDescription ??
                                                    "",
                                            longDesc:
                                                timeline?.longDescription ?? "",
                                            time: timeline?.time ?? "N/A",
                                          );
                                        },
                                      )
                                    ],
                                  ),
                                ),
                        ),
                      ],
                    ),
                  )
              ],
            ),
          );
        },
      ),
    );
  }
}

class TimelineWidget extends StatelessWidget {
  const TimelineWidget({
    super.key,
    required this.shortDesc,
    required this.longDesc,
    required this.time,
  });

  final String shortDesc;
  final String longDesc;
  final String time;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgPicture.asset(AppSvgs.step),
        XBox(10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                shortDesc,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style:
                    AppTypography.text14.withCustomColor(AppColors.primaryBlue),
              ),
              YBox(2),
              Text(
                longDesc,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: AppTypography.text12.withCustomColor(
                  AppColors.black.withValues(alpha: 0.85),
                ),
              ),
            ],
          ),
        ),
        XBox(10),
        Text(
          time,
          style: AppTypography.text14.withCustomColor(
            AppColors.black.withValues(alpha: 0.85),
          ),
        ),
      ],
    );
  }
}
