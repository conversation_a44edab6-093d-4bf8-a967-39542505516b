import 'dart:io';

import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  final _fullNameC = TextEditingController();
  final _phoneC = TextEditingController();
  final _emailC = TextEditingController();

  final _fullNameF = FocusNode();
  final _phoneF = FocusNode();
  final _emailF = FocusNode();

  File? _imageFile;
  bool loadingImage = false;
  String? avatarUrl;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() async {
    final user = ref.read(authVmodel).user;

    if (user != null) {
      _fullNameC.text = user.name ?? "";
      _phoneC.text = user.phone ?? "";
      _emailC.text = user.email ?? "";
      avatarUrl = user.avatar;
    }

    setState(() {});
  }

  @override
  void dispose() {
    _fullNameC.dispose();
    _phoneC.dispose();
    _emailC.dispose();

    _fullNameF.dispose();
    _phoneF.dispose();
    _emailF.dispose();

    super.dispose();
  }

  bool get emailIsEmpty => ref.watch(authVmodel).user?.email?.isEmpty ?? true;
  bool get phoneIsEmpty => ref.watch(authVmodel).user?.phone?.isEmpty ?? true;

  bool get nameHasChanged =>
      _fullNameC.text.trim() != (ref.watch(authVmodel).user?.name ?? "");
  bool get phoneHasChanged =>
      _phoneC.text.trim() != (ref.watch(authVmodel).user?.phone ?? "");
  bool get emailHasChanged =>
      _emailC.text.trim() != (ref.watch(authVmodel).user?.email ?? "");

  bool get enableBtn =>
      nameHasChanged ||
      phoneHasChanged ||
      emailHasChanged ||
      _imageFile != null;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Sizer.screenWidth,
      height: Sizer.screenHeight,
      child: BusyOverlay(
        show: ref.read(authVmodel).isBusy,
        child: Scaffold(
          appBar: CustomAppbar(
            title: "Profile",
            trailingWidget: InkWell(
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.notificationScreen);
                },
                child: SvgPicture.asset(AppSvgs.notification)),
            leadingWidget: Container(
              height: Sizer.height(40),
              width: Sizer.width(40),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(Sizer.radius(40)),
                  border: Border.all(
                    color: AppColors.primaryBlue,
                    width: 2,
                  )),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(Sizer.radius(40)),
                child: ref.watch(authVmodel).user?.avatar != null
                    ? MyCachedNetworkImage(
                        imageUrl: ref.watch(authVmodel).user!.avatar,
                        fit: BoxFit.cover,
                      )
                    : Icon(
                        Iconsax.user,
                        size: Sizer.width(20),
                      ),
              ),
            ),
          ),
          body: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            children: [
              const YBox(30),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: _pickImage,
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(
                            Sizer.radius(100),
                          ),
                          child: Container(
                            height: Sizer.height(90),
                            width: Sizer.height(90),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: AppColors.neutral5,
                                width: Sizer.width(2),
                              ),
                              borderRadius:
                                  BorderRadius.circular(Sizer.radius(100)),
                            ),
                            child: loadingImage ||
                                    ref.read(fileUploadVm).busy(uploadState)
                                ? LoaderIcon(
                                    size: 30,
                                    color: AppColors.neutral5,
                                  )
                                : (_imageFile != null || avatarUrl != null)
                                    ? MyCachedNetworkImage(
                                        imageUrl: avatarUrl!,
                                        fit: BoxFit.cover,
                                      )
                                    : Icon(
                                        Iconsax.user,
                                        size: Sizer.width(50),
                                      ),
                          ),
                        ),
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: InkWell(
                            onTap: _pickImage,
                            child: Container(
                              padding: EdgeInsets.all(Sizer.radius(4)),
                              decoration: BoxDecoration(
                                color: AppColors.grayF2,
                                borderRadius:
                                    BorderRadius.circular(Sizer.radius(4)),
                              ),
                              child: SvgPicture.asset(
                                AppSvgs.pen,
                                height: Sizer.height(16),
                                width: Sizer.width(16),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              YBox(10),
              CustomTextField(
                controller: _fullNameC,
                focusNode: _fullNameF,
                showLabelHeader: true,
                labelText: "Full Name",
                hintText: "Enter first name",
                fillColor: AppColors.neutral3,
              ),
              YBox(10),
              CustomTextField(
                controller: _phoneC,
                focusNode: _phoneF,
                showLabelHeader: true,
                labelText: "Phone Number",
                hintText: "Enter phone number",
                keyboardType: KeyboardType.phone,
                fillColor: AppColors.neutral3,
                isReadOnly: !phoneIsEmpty,
                suffixIcon: phoneIsEmpty
                    ? null
                    : Icon(
                        Iconsax.lock,
                        size: Sizer.height(16),
                        color: AppColors.neutral6,
                      ),
                onChanged: (p0) => setState(() {}),
              ),
              YBox(10),
              CustomTextField(
                controller: _emailC,
                focusNode: _emailF,
                showLabelHeader: true,
                labelText: "Email Address",
                hintText: "Enter your email",
                fillColor: AppColors.neutral3,
                isReadOnly: !emailIsEmpty,
                suffixIcon: emailIsEmpty
                    ? null
                    : Icon(
                        Iconsax.lock,
                        size: Sizer.height(16),
                        color: AppColors.neutral6,
                      ),
              ),
              YBox(140),
              CustomBtn.solid(
                onTap: _updateProfile,
                online: enableBtn,
                text: "Save Changes",
              ),
            ],
          ),
        ),
      ),
    );
  }

  _updateProfile() async {
    FocusScope.of(context).unfocus();
    final r = await ref.read(authVmodel).updateProfile(
          name: nameHasChanged ? _fullNameC.text.trim() : null,
          phone: phoneHasChanged ? _phoneC.text.trim() : null,
          email: emailHasChanged ? _emailC.text.trim() : null,
          avatar: _imageFile != null ? avatarUrl : null,
        );

    handleApiResponse(
      response: r,
      onSuccess: () {
        // Navigator.pushNamed(context, RoutePath.bottomNavScreen);
        ref.read(authVmodel).getUser();
      },
    );
  }

  Future<void> _pickImage() async {
    setState(() => loadingImage = true);
    try {
      final pickedFile = await ImagePickerUtils.pickImage();

      if (pickedFile.isNotEmpty) {
        final croppedFile =
            await ImagePickerUtils.cropImage(image: pickedFile.first);
        if (croppedFile != null) {
          setState(() => _imageFile = croppedFile);

          final r =
              await ref.read(fileUploadVm).uploadFile(file: [croppedFile]);
          if (r.success && r.data != null && r.data!.isNotEmpty) {
            setState(() => avatarUrl = r.data!.first.url);
          }
        }
      }
    } catch (e) {
      FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning, message: e.toString());
    } finally {
      setState(() => loadingImage = false);
    }
  }
}
